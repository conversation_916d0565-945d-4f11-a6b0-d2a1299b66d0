<?php

/**
 * Debug Tenant System
 * Shows which tenant is detected for current domain
 */

require_once 'includes/config.php';
require_once 'includes/Database.php';
require_once 'includes/TenantContext.php';

echo "<!DOCTYPE html><html><head><title>Tenant Debug</title>";
echo "<style>body{font-family:Arial,sans-serif;margin:20px;} .info{background:#e7f3ff;padding:15px;border-radius:5px;margin:10px 0;} .error{background:#f8d7da;padding:15px;border-radius:5px;margin:10px 0;}</style>";
echo "</head><body>";

echo "<h1>🔍 Tenant System Debug</h1>";

// Current URL info
echo "<div class='info'>";
echo "<h3>📋 Current Request Info:</h3>";
echo "<strong>Full URL:</strong> " . (isset($_SERVER['HTTPS']) ? 'https' : 'http') . '://' . $_SERVER['HTTP_HOST'] . $_SERVER['REQUEST_URI'] . "<br>";
echo "<strong>Host:</strong> " . $_SERVER['HTTP_HOST'] . "<br>";
echo "<strong>Server Name:</strong> " . $_SERVER['SERVER_NAME'] . "<br>";
echo "</div>";

// Try to initialize tenant context
echo "<div class='info'>";
echo "<h3>🏢 Tenant Detection:</h3>";

try {
    // Manual tenant detection
    $host = $_SERVER['HTTP_HOST'];
    $parts = explode('.', $host);

    echo "<strong>Host parts:</strong> " . implode(' | ', $parts) . "<br>";

    if (count($parts) >= 3) {
        $subdomain = $parts[0];
        echo "<strong>Detected subdomain:</strong> '$subdomain'<br>";
    } else {
        echo "<strong>No subdomain detected</strong> (main domain)<br>";
        $subdomain = null;
    }

    // Initialize tenant context
    TenantContext::initializeFromRequest();
    $currentTenant = TenantContext::getCurrentTenant();

    if ($currentTenant) {
        echo "<strong>✅ Current Tenant ID:</strong> " . $currentTenant . "<br>";

        // Get tenant details
        $db = Database::getInstance();
        $conn = $db->getConnection();
        $stmt = $conn->prepare("SELECT * FROM tenants WHERE id = :id");
        $stmt->bindValue(':id', $currentTenant);
        $result = $stmt->execute();
        $tenant = $result->fetchArray(SQLITE3_ASSOC);

        if ($tenant) {
            echo "<strong>Business Name:</strong> " . htmlspecialchars($tenant['business_name']) . "<br>";
            echo "<strong>Subdomain:</strong> " . htmlspecialchars($tenant['subdomain']) . "<br>";
            echo "<strong>Status:</strong> " . htmlspecialchars($tenant['status']) . "<br>";
        }
    } else {
        echo "<strong>❌ No tenant detected!</strong><br>";
    }
} catch (Exception $e) {
    echo "<div class='error'>";
    echo "<strong>❌ Error:</strong> " . htmlspecialchars($e->getMessage());
    echo "</div>";
}

echo "</div>";

// Test database queries
echo "<div class='info'>";
echo "<h3>📊 Database Test:</h3>";

try {
    $db = Database::getInstance();
    $conn = $db->getConnection();

    // Count records without tenant filter
    $result = $conn->query("SELECT COUNT(*) as count FROM customers");
    $totalCustomers = $result->fetchArray()['count'];
    echo "<strong>Total customers (no filter):</strong> $totalCustomers<br>";

    // Count records with current tenant filter
    if ($currentTenant) {
        $stmt = $conn->prepare("SELECT COUNT(*) as count FROM customers WHERE tenant_id = :tenant_id");
        $stmt->bindValue(':tenant_id', $currentTenant);
        $result = $stmt->execute();
        $tenantCustomers = $result->fetchArray()['count'];
        echo "<strong>Customers for current tenant:</strong> $tenantCustomers<br>";
    }

    // Show all tenants
    echo "<br><strong>All tenants in database:</strong><br>";
    $result = $conn->query("SELECT id, business_name, subdomain, status FROM tenants");
    while ($row = $result->fetchArray(SQLITE3_ASSOC)) {
        $current = ($row['id'] === $currentTenant) ? " ← CURRENT" : "";
        echo "- {$row['business_name']} ({$row['subdomain']}) - {$row['id']}$current<br>";
    }
} catch (Exception $e) {
    echo "<div class='error'>";
    echo "<strong>❌ Database Error:</strong> " . htmlspecialchars($e->getMessage());
    echo "</div>";
}

echo "</div>";

// Test links
echo "<div class='info'>";
echo "<h3>🔗 Test Links:</h3>";
echo "<a href='https://skrtz.gr/debug_tenant.php'>Main Domain Debug</a><br>";
echo "<a href='https://realma.skrtz.gr/debug_tenant.php'>Realma Subdomain Debug</a><br>";
echo "<a href='https://test.skrtz.gr/debug_tenant.php'>Test Subdomain Debug</a><br>";
echo "</div>";

echo "</body></html>";
