<?php
// Web-based migration script - no user input required
echo "<!DOCTYPE html><html><head><title>Tenant Migration</title></head><body>";
echo "<h1>🚀 Tenant System Migration</h1>";
echo "<div style='font-family: monospace; background: #f5f5f5; padding: 20px; border-radius: 5px;'>";

try {
    require_once 'includes/migrations/add_tenant_system.php';

    echo "<strong>Starting migration...</strong><br><br>";

    // Run migration without user confirmation
    $success = migrate_add_tenant_system(true);

    if ($success) {
        echo "<br><div style='color: green; font-weight: bold; font-size: 18px;'>";
        echo "✅ MIGRATION COMPLETED SUCCESSFULLY!";
        echo "</div><br>";

        echo "<strong>Next steps:</strong><br>";
        echo "1. Delete this run_migration.php file<br>";
        echo "2. Go to your admin panel: <a href='admin/'>admin/</a><br>";
        echo "3. Your system now supports multiple tenants!<br><br>";

        echo "<strong>To test:</strong><br>";
        echo "- Your current system works exactly as before<br>";
        echo "- You can now create new businesses with subdomains<br>";
    } else {
        echo "<div style='color: red; font-weight: bold;'>";
        echo "❌ Migration failed. Check error messages above.";
        echo "</div>";
    }
} catch (Exception $e) {
    echo "<div style='color: red; font-weight: bold;'>";
    echo "❌ Error: " . htmlspecialchars($e->getMessage());
    echo "</div>";
}

echo "</div>";
echo "<br><p><strong>⚠️ Important: Delete this file after migration is complete!</strong></p>";
echo "</body></html>";
